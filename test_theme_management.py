#!/usr/bin/env python3
"""
Test script for theme management endpoints.

This script demonstrates how to use the new theme create and update endpoints
with background_color and font_color fields.
"""

import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8204/v1/management"  # Adjust based on your setup
HEADERS = {
    "Content-Type": "application/json",
    # Add authentication headers as needed
    # "Authorization": "Bearer your-token-here"
}

def create_theme_example() -> Dict[str, Any]:
    """Example of creating a new theme with colors."""
    
    theme_data = {
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "description": "नेपाली संस्कृति र परम्पराका बारेमा",
        "description_en": "About Nepali culture and traditions",
        "category": "culture",
        "icon": "🏛️",
        "background_color": "#FF6B6B",  # Red background
        "font_color": "#FFFFFF",        # White text
        "is_active": True
    }
    
    print("Creating new theme...")
    print(f"Theme data: {json.dumps(theme_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/themes",
            headers=HEADERS,
            json=theme_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Theme created successfully!")
            print(f"Created theme ID: {result['data']['_id']}")
            return result['data']
        else:
            print(f"❌ Failed to create theme: {response.status_code}")
            print(f"Error: {response.text}")
            return {}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {}

def update_theme_colors_example(theme_id: str) -> Dict[str, Any]:
    """Example of updating theme colors."""
    
    update_data = {
        "background_color": "#4ECDC4",  # Teal background
        "font_color": "#2C3E50"         # Dark blue text
    }
    
    print(f"\nUpdating theme colors for ID: {theme_id}")
    print(f"Update data: {json.dumps(update_data, indent=2)}")
    
    try:
        response = requests.put(
            f"{BASE_URL}/themes/{theme_id}",
            headers=HEADERS,
            json=update_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Theme colors updated successfully!")
            return result['data']
        else:
            print(f"❌ Failed to update theme: {response.status_code}")
            print(f"Error: {response.text}")
            return {}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {}

def update_theme_colors_patch_example(theme_id: str) -> Dict[str, Any]:
    """Example of updating only colors using the PATCH endpoint."""
    
    print(f"\nUpdating only background color using PATCH for ID: {theme_id}")
    
    try:
        response = requests.patch(
            f"{BASE_URL}/themes/{theme_id}/colors?background_color=%23FFD93D&font_color=%23000000",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Theme colors updated successfully with PATCH!")
            return result['data']
        else:
            print(f"❌ Failed to update theme colors: {response.status_code}")
            print(f"Error: {response.text}")
            return {}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {}

def get_theme_example(theme_id: str) -> Dict[str, Any]:
    """Example of getting theme details."""
    
    print(f"\nGetting theme details for ID: {theme_id}")
    
    try:
        response = requests.get(
            f"{BASE_URL}/theme/{theme_id}",
            headers=HEADERS
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Theme details retrieved successfully!")
            theme_data = result['data']
            print(f"Theme: {theme_data.get('name_en', 'N/A')}")
            print(f"Background Color: {theme_data.get('background_color', 'N/A')}")
            print(f"Font Color: {theme_data.get('font_color', 'N/A')}")
            return theme_data
        else:
            print(f"❌ Failed to get theme: {response.status_code}")
            print(f"Error: {response.text}")
            return {}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {}

def main():
    """Main function to run the examples."""
    
    print("🎨 Theme Management API Test")
    print("=" * 40)
    
    # Step 1: Create a new theme
    created_theme = create_theme_example()
    
    if not created_theme:
        print("❌ Cannot continue without creating a theme first")
        return
    
    theme_id = created_theme.get('_id')
    if not theme_id:
        print("❌ No theme ID returned from creation")
        return
    
    # Step 2: Update theme colors using PUT
    updated_theme = update_theme_colors_example(theme_id)
    
    # Step 3: Update theme colors using PATCH
    patch_updated_theme = update_theme_colors_patch_example(theme_id)
    
    # Step 4: Get final theme details
    final_theme = get_theme_example(theme_id)
    
    print("\n🎉 Theme management test completed!")
    print(f"Final theme colors:")
    print(f"  Background: {final_theme.get('background_color', 'N/A')}")
    print(f"  Font: {final_theme.get('font_color', 'N/A')}")

if __name__ == "__main__":
    main()