# User Invitation Flow Documentation

## Overview
The user invitation system allows admins to invite new users (agents, supervisors) to the platform using secure JWT tokens.

## Flow Steps

### 1. Admin Creates Invitation

**Endpoint**: `POST /v1/auth/users/invite`

**Headers**:
```
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "username": "new_user",
  "role": "agent"  // or "supervisor", "admin"
}
```

**Response**:
```json
{
  "registration_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "success": true,
  "msg": "Token Generated!"
}
```

**What Happens**:
- ✅ Checks if username already exists
- ✅ Deletes any existing invitations for the username
- ✅ Creates JWT token with 7-day expiration
- ✅ Stores invitation record in database
- ✅ Returns registration token

### 2. Share Invitation

**Admin shares the token with the invited user via**:
- Email (manual)
- Direct message
- Registration link: `https://yourapp.com/register?token=<token>`

### 3. User Registration

**Endpoint**: `POST /v1/auth/users/register`

**Request Body**:
```json
{
  "username": "new_user",
  "role": "agent",
  "password": "user_chosen_password",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response Success**:
```json
{
  "msg": "Agent registered successfully",
  "success": true
}
```

**Response Errors**:
```json
{
  "msg": "Invalid invitation token!",
  "success": false
}
```

**What Happens**:
- ✅ Verifies JWT token signature and expiration
- ✅ Checks invitation exists in database
- ✅ Validates invitation hasn't been used
- ✅ Creates new user account
- ✅ Marks invitation as used
- ✅ User can now login

## Database Collections

### Invitations Collection
```json
{
  "_id": "ObjectId",
  "username": "invited_username",
  "token": "jwt_token_string",
  "role": "agent|supervisor|admin",
  "invited_by": "admin_tenant_id",
  "expires_at": "2024-01-22T10:30:00Z",
  "used": false
}
```

### Users Collection (After Registration)
```json
{
  "_id": "ObjectId",
  "username": "new_user",
  "hashed_password": "argon2_hash",
  "role": "agent",
  "created_by": "admin_tenant_id",
  "created_at": "2024-01-15T10:30:00Z",
  "onboarding_completed": false
}
```

## Security Features

### JWT Token Structure
```json
{
  "username": "invited_user",
  "invited_by": "admin_tenant_id",
  "role": "agent",
  "tenant_id": "tenant_id",
  "exp": **********  // 7 days from creation
}
```

### Validation Checks
- ✅ **Token Signature**: Verified with SECRET_KEY
- ✅ **Token Expiration**: 7-day validity
- ✅ **Database Verification**: Token must exist in invitations collection
- ✅ **Single Use**: Token marked as used after registration
- ✅ **Username Uniqueness**: Prevents duplicate usernames

## Error Handling

### Common Errors
1. **Username Exists**: `"Username already exists"`
2. **Invalid Token**: `"Invalid invitation token!"`
3. **Expired Token**: `"Invitation token has expired"`
4. **Used Token**: `"Invitation token has already been used"`

### Token Expiration
- **Default**: 7 days from creation
- **Cleanup**: Expired tokens remain in database but fail validation
- **Regeneration**: Admin can create new invitation for same username

## Example Usage

### Complete Flow Example
```bash
# 1. Admin creates invitation
curl -X POST 'http://localhost:8204/v1/auth/users/invite' \
  -H 'Authorization: Bearer <admin_token>' \
  -H 'Content-Type: application/json' \
  -d '{"username": "john_doe", "role": "agent"}'

# Response: {"registration_token": "eyJ...", "success": true}

# 2. User registers with token
curl -X POST 'http://localhost:8204/v1/auth/users/register' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "john_doe",
    "role": "agent",
    "password": "securepassword123",
    "token": "eyJ..."
  }'

# Response: {"msg": "Agent registered successfully", "success": true}

# 3. User can now login
curl -X POST 'http://localhost:8204/v1/auth/login' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'username=john_doe&password=securepassword123'
```

## Frontend Integration

### Registration Page Component
```javascript
// Extract token from URL
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');

// Registration form submission
const registerUser = async (formData) => {
  const response = await fetch('/v1/auth/users/register', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      username: formData.username,
      role: formData.role,
      password: formData.password,
      token: token
    })
  });
  
  const result = await response.json();
  if (result.success) {
    // Redirect to login page
    window.location.href = '/login';
  } else {
    // Show error message
    alert(result.msg);
  }
};
```

## Next Steps for Your Current Invitation

**Your Token**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.u0O88i7GgU1I3UPjY2HC9G3Ye3UWmGrJr9ZkeuKEHZ8`

**To complete registration, run**:
```bash
curl -X POST 'http://localhost:8204/v1/auth/users/register' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "diwas",
    "role": "agent",
    "password": "your_chosen_password",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.u0O88i7GgU1I3UPjY2HC9G3Ye3UWmGrJr9ZkeuKEHZ8"
  }'
```

**Token expires**: January 21, 2025 (7 days from creation)
