# Theme Management API Documentation

This document describes the new theme management endpoints for creating and updating themes with background and font color support.

## Overview

The theme management API provides endpoints to:
- Create new themes with background and font colors
- Update existing themes (full update)
- Update only theme colors (partial update)

## Endpoints

### 1. Create Theme

**POST** `/v1/management/themes`

Creates a new theme with background and font color fields.

#### Request Body

```json
{
  "name": "नेपाली संस्कृति",
  "name_en": "Nepali Culture",
  "description": "नेपाली संस्कृति र परम्पराका बारेमा",
  "description_en": "About Nepali culture and traditions",
  "category": "culture",
  "icon": "🏛️",
  "background_color": "#FF6B6B",
  "font_color": "#FFFFFF",
  "is_active": true
}
```

#### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Theme name in Nepali |
| `name_en` | string | Yes | Theme name in English |
| `description` | string | No | Theme description in Nepali |
| `description_en` | string | No | Theme description in English |
| `category` | string | Yes | Theme category (culture, geography, history, etc.) |
| `icon` | string | No | Theme icon/emoji (default: "🎨") |
| `background_color` | string | No | Background color in hex format (default: "#4ECDC4") |
| `font_color` | string | No | Font color in hex format (default: "#FFFFFF") |
| `is_active` | boolean | No | Whether the theme is active (default: true) |

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "65f1234567890abcdef12345",
    "name": "नेपाली संस्कृति",
    "name_en": "Nepali Culture",
    "description": "नेपाली संस्कृति र परम्पराका बारेमा",
    "description_en": "About Nepali culture and traditions",
    "category": "culture",
    "icon": "🏛️",
    "background_color": "#FF6B6B",
    "font_color": "#FFFFFF",
    "color": "#FF6B6B",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "created_by": "user_id_here"
  },
  "message": "Theme created successfully",
  "metadata": {
    "timestamp": null,
    "request_id": null
  }
}
```

### 2. Update Theme

**PUT** `/v1/management/themes/{theme_id}`

Updates an existing theme. Only provided fields will be updated.

#### Request Body

```json
{
  "background_color": "#4ECDC4",
  "font_color": "#2C3E50",
  "is_active": true
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "65f1234567890abcdef12345",
    "name": "नेपाली संस्कृति",
    "name_en": "Nepali Culture",
    "background_color": "#4ECDC4",
    "font_color": "#2C3E50",
    "color": "#4ECDC4",
    "updated_at": "2024-01-15T11:00:00Z",
    // ... other fields
  },
  "message": "Theme updated successfully"
}
```

### 3. Update Theme Colors Only

**PATCH** `/v1/management/themes/{theme_id}/colors`

Updates only the background and/or font colors of a theme.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `background_color` | string | No | New background color in hex format |
| `font_color` | string | No | New font color in hex format |

At least one color parameter must be provided.

#### Example Request

```
PATCH /v1/management/themes/65f1234567890abcdef12345/colors?background_color=%23FFD93D&font_color=%23000000
```

#### Response

```json
{
  "success": true,
  "data": {
    "_id": "65f1234567890abcdef12345",
    "background_color": "#FFD93D",
    "font_color": "#000000",
    "color": "#FFD93D",
    "updated_at": "2024-01-15T11:15:00Z",
    // ... other fields
  },
  "message": "Theme colors updated successfully"
}
```

## Color Format

All color fields should be provided in hexadecimal format:
- Valid: `#FF6B6B`, `#4ECDC4`, `#FFFFFF`
- Invalid: `red`, `rgb(255,0,0)`, `hsl(0,100%,50%)`

## Error Responses

### 400 Bad Request

```json
{
  "detail": "Invalid theme ID format"
}
```

### 404 Not Found

```json
{
  "detail": "Theme not found"
}
```

### 400 Duplicate Name

```json
{
  "detail": "Theme with this name already exists"
}
```

## Usage Examples

### Creating a Theme with Custom Colors

```python
import requests

theme_data = {
    "name": "हिमालयी प्रकृति",
    "name_en": "Himalayan Nature",
    "category": "geography",
    "background_color": "#2E8B57",  # Sea Green
    "font_color": "#F0F8FF",        # Alice Blue
    "icon": "🏔️"
}

response = requests.post(
    "http://localhost:8204/v1/management/themes",
    json=theme_data,
    headers={"Content-Type": "application/json"}
)
```

### Updating Only Colors

```python
import requests

theme_id = "65f1234567890abcdef12345"
response = requests.patch(
    f"http://localhost:8204/v1/management/themes/{theme_id}/colors",
    params={
        "background_color": "#FF4757",
        "font_color": "#FFFFFF"
    }
)
```

### Updating Multiple Fields

```python
import requests

update_data = {
    "name_en": "Updated Theme Name",
    "background_color": "#3742FA",
    "font_color": "#FFFFFF",
    "is_active": False
}

response = requests.put(
    f"http://localhost:8204/v1/management/themes/{theme_id}",
    json=update_data,
    headers={"Content-Type": "application/json"}
)
```

## Backward Compatibility

The new theme management endpoints maintain backward compatibility:
- The existing `color` field is automatically updated when `background_color` is set
- Existing themes without `font_color` will use the default white color
- All existing theme endpoints continue to work as before

## Integration with Existing Features

The new color fields integrate seamlessly with:
- Curated content generation (themes with colors)
- Today's theme selection (includes color information)
- Theme filtering and search (color-aware responses)

## Testing

Use the provided test script `test_theme_management.py` to test the new endpoints:

```bash
python test_theme_management.py
```

Make sure to update the `BASE_URL` and authentication headers in the test script according to your setup.