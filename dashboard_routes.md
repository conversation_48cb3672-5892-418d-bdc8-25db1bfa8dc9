# Admin Dashboard API Routes

## Authentication
All routes require admin role authentication.

**Header Required:**
```
Authorization: <PERSON><PERSON> <jwt_token>
```

---

## 1. Basic Dashboard Overview

**GET** `/v1/management/dashboard`

### Parameters
None

### Returns
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "overview": {
    "total_users": 150,
    "users_joined_today": 5,
    "users_active_today": 23,
    "total_task_sets": 450,
    "task_sets_created_today": 12,
    "task_sets_completed_today": 8
  }
}
```

---

## 2. User Metrics (Date Range)

**GET** `/v1/management/dashboard/users`

### Parameters
- `start_date` (optional): YYYY-MM-DD format, defaults to 7 days ago
- `end_date` (optional): YYYY-MM-DD format, defaults to today

### Example
```
GET /v1/management/dashboard/users?start_date=2024-01-01&end_date=2024-01-07
```

### Returns
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-07"
  },
  "summary": {
    "total_users": 150,
    "users_in_period": 25
  },
  "daily_data": {
    "registrations": [
      {"_id": "2024-01-01", "count": 3},
      {"_id": "2024-01-02", "count": 5}
    ],
    "active_users": [
      {"_id": "2024-01-01", "count": 12},
      {"_id": "2024-01-02", "count": 18}
    ]
  },
  "distribution": {
    "by_role": [
      {"_id": "user", "count": 120},
      {"_id": "admin", "count": 5}
    ]
  }
}
```

---

## 3. Task Sets Metrics (Date Range)

**GET** `/v1/management/dashboard/task-sets`

### Parameters
- `start_date` (optional): YYYY-MM-DD format, defaults to today
- `end_date` (optional): YYYY-MM-DD format, defaults to today

### Example
```
GET /v1/management/dashboard/task-sets?start_date=2024-01-01&end_date=2024-01-07
```

### Returns
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-07"
  },
  "summary": {
    "total_task_sets_all_time": 450,
    "total_task_items_all_time": 1800,
    "task_sets_created_in_range": 35,
    "task_sets_completed_in_range": 28,
    "task_items_created_in_range": 140
  },
  "task_sets": {
    "by_generation_type": [
      {"_id": "primary", "count": 20},
      {"_id": "followup", "count": 10},
      {"_id": "curated", "count": 5}
    ],
    "by_input_type": [
      {"_id": "text", "count": 25},
      {"_id": "audio", "count": 8},
      {"_id": "video", "count": 2}
    ],
    "by_status": [
      {"_id": "completed", "count": 28},
      {"_id": "pending", "count": 7}
    ],
    "daily_creation": [
      {"_id": "2024-01-01", "count": 5},
      {"_id": "2024-01-02", "count": 8}
    ],
    "performance_stats": [
      {
        "_id": null,
        "avg_score": 75.5,
        "avg_total_score": 100,
        "avg_completion_time": 1800000,
        "total_tasks_completed": 112,
        "total_score_earned": 8456
      }
    ]
  },
  "task_items": {
    "by_type": [
      {"_id": "multiple_choice", "count": 80},
      {"_id": "single_choice", "count": 40},
      {"_id": "speak_word", "count": 20}
    ],
    "by_verification_status": [
      {"_id": "verified", "count": 100},
      {"_id": "pending", "count": 40}
    ]
  }
}
```

---

## 4. User Details

**GET** `/v1/management/dashboard/user/{user_id}`

### Parameters
- `user_id` (path): User ID to get details for

### Example
```
GET /v1/management/dashboard/user/507f1f77bcf86cd799439011
```

### Returns
```json
{
  "user_info": {
    "id": "507f1f77bcf86cd799439011",
    "username": "john_doe",
    "role": "user",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T10:00:00Z",
    "last_login": "2024-01-15T09:30:00Z"
  },
  "activity_summary": {
    "total_task_sets": 15,
    "completed_task_sets": 12,
    "total_score_earned": 1250,
    "total_possible_score": 1500,
    "avg_score": 83.3
  },
  "task_distribution": {
    "by_status": [
      {"_id": "completed", "count": 12},
      {"_id": "pending", "count": 3}
    ],
    "by_type": [
      {"_id": "text", "count": 10},
      {"_id": "audio", "count": 5}
    ]
  },
  "recent_activity": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "input_type": "text",
      "status": "completed",
      "created_at": "2024-01-14T15:30:00Z",
      "scored": 85,
      "total_score": 100
    }
  ]
}
```

---

## Task Generation Types

- **primary**: Regular user-generated tasks
- **followup**: Follow-up tasks based on previous tasks
- **curated**: Theme-based curated content

## Time Boundaries

- Start date: 00:00:00 UTC
- End date: 23:59:59 UTC
